// Enum 类：表示单个枚举项

export class EnumItem {
	constructor(public readonly code: string|number, public readonly title: string) {}

	// 转换为选项对象 { label, value }
	toOption() {
		return { label: this.title, value: this.code };
	}
}

// Enums 抽象类：提供通用功能和静态方法
export abstract class Enum {
	// 静态方法：动态获取所有静态的 Enum 实例
	static getAll(): EnumItem[] {
		const staticProperties = Object.getOwnPropertyNames(this)
			.filter((key) => this[key] instanceof EnumItem) // 筛选出是 Enum 实例的属性
			.map((key) => this[key]); // 获取对应的值
		return staticProperties;
	}

	// 静态方法：通过 code 获取 title
	static getTitleByCode(code: string|number): string | undefined {
		const item = this.getAll().find((item) => item.code+"" === code+"");
		return item?.title;
	}

	// 静态方法：通过 title 获取 code
	static getCodeByTitle(title: string): number|string | undefined {
		const item = this.getAll().find((item) => item.title === title);
		return item?.code;
	}

	// 静态方法：获取 options 数组
	static getOptions(): { label: string; value: string|number }[] {
		return this.getAll().map((item) => item.toOption());
	}

	
}

