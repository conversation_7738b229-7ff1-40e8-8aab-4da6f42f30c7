<template>
	<a-cascader
    v-model="valueModel"
    :options="dictDataTree"
    :multiple="props.multiple"
    :allow-search="true"
		:check-strictly="checkStrictly"
    allow-clear
    placeholder="请选择"
    @change="(value) => emits('selected',value)"
  />
</template>

<script setup lang="ts">
import { useDictStore } from '@arco/store'
import { ref, watch } from 'vue'
import type { TreeNodeData } from '@arco-design/web-vue/es/tree/interface'
import { BusinessTypeEnum, DictTypeEnum } from '@/dict/systemManage.ts'
import { CascaderOption } from '@arco-design/web-vue/es/cascader/interface'

const dictStore = useDictStore()


const valueModel = defineModel()

const props = withDefaults( defineProps<{
	dictType: DictTypeEnum,
	//严格模式
	checkStrictly?:boolean,
	//自动增加一个空值
	appendNullValue?: boolean,
	multiple?: boolean,
	//用哪个值作为modleValue,tag,id,key
	modelValueWitch?: 'key' | 'tag' | 'id',
	//筛选对应的业务类型
	businessType?:BusinessTypeEnum,
	//允许忽略一些key
	ignoreKeys?:[]
}>(),{
	appendNullValue:false,
	checkStrictly:false,
	multiple:false,
	useTagForKey:false,
	modelValueWitch:'key'
})

const emits = defineEmits(['selected']);

// 树形数据
const dictDataTree = ref<CascaderOption[]>([])

const dictDataListBuildTreeData = function(list:REQUEST_GET_DICT_VALUE_LIST_TYPE[]): TreeNodeData[] {

	let newTree: CascaderOption[] = []

	for (let dictData of list) {
		// 如果指定了业务类型，则只显示对应业务类型的数据
		if (props.businessType && dictData.businessType && dictData.businessType !== props.businessType) {
			continue
		}
		//如果在ignore列表中就跳过
		if (props.ignoreKeys && dictData.dictKey in props.ignoreKeys) {
			continue;
		}

		let children: TreeNodeData[] = []

		//子孙存在就构建
		if (dictData.children && dictData.children.length > 0) {
			children = dictDataListBuildTreeData(dictData.children)
		}


		let treeKey:string|number = dictData.dictKey;

		if (props.modelValueWitch === 'tag') {
			treeKey = dictData.dictTag
		}else if (props.modelValueWitch === 'id') {
			treeKey = dictData.dictDataId
		}

		const tag = dictData['dictTag']
		newTree.push({
			label: tag,
			value: treeKey,
			children: children.length==0?undefined:children,
			disabled: dictData['enableFlag']==0
		})
	}

	return newTree
}


//监听dictId改变，以及数据源更新
watch([dictStore,()=>props.dictType,()=>props.businessType],async ()=>{
	if(!props.dictType){
		return
	}
	let dictDataList = await dictStore.listTreeDictDataByType(props.dictType)

	if(!dictDataList){
		return
	}

	let newTree: CascaderOption[] =dictDataListBuildTreeData(dictDataList)

	// 如果指定了业务类型，直接过滤
	if (props.businessType) {

	}

	if (props.appendNullValue) {
		newTree.unshift({
			label: '空',
			value: -1
			// value: -1
		})
	}

	dictDataTree.value = newTree
},{
	immediate:true
})

//查询时的规则
const filterTreeNode = (searchValue, nodeData) => {
	return nodeData.label.toLowerCase().indexOf(searchValue.toLowerCase()) > -1
}
</script>

<style scoped lang="scss"></style>
