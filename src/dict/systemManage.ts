export const appearanceEnableStatusptions = [
  { label: '已启用', value: '1' },
  { label: '未启用', value: '0' }
]

export const templateStatusOptions = [
  { label: '禁用', value: 0 },
  { label: '启用', value: 1 }
]

export const templateTypeOptions = [
  { label: '推广短信', value: 2 },
  { label: '通知短信', value: 1 },
  { label: '验证码', value: 0 }
]

export const operateTypeOptions = [
  { label: '查询', value: '查询' },
  { label: '新增', value: '新增' },
  { label: '修改', value: '修改' },
  { label: '删除', value: '删除' },
  { label: '其他', value: '其他' }
]


// 定义 dictType 的枚举类型
export enum DictTypeEnum {
	NATURE = 'nature',
	case_close_reason = 'case_close_reason',
	case_suspension_of_mediation = 'case_suspension_of_mediation',
	litigant_identity_type = 'litigant_identity_type',
	mediate_status = 'mediate_status',
	MEDIATE_TYPE = 'mediate_type',
	ORG_TYPE = 'org_type',
	TASK_STATUS = 'task_status',
	TASK_TYPE = 'task_type',
	LITIGANT_CONTACT_STATUS = 'litigant_contact_status',
	// 可以根据实际需求扩展更多类型
}

export enum BusinessTypeEnum {
	government = '1',
	finance = '2',
}


export default {
  appearanceEnableStatusptions,
  templateStatusOptions,
  templateTypeOptions,
  operateTypeOptions,
	DictTypeEnum
}
