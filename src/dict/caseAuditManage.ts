import { EnumItem, Enum } from '@/utils/enumItem.ts'

const approvalTypeOptions = [
  { label: '调解办结', value: '1' },
  { label: '延期审批', value: '2' },
  { label: '案管办结', value: '3' }
]


// 审批状态枚举类
export class ApprovalStatusEnum extends Enum {
	static readonly MEDIATE_CLOSE = new EnumItem(1, "调解办结");
	static readonly DELAY = new EnumItem(2, "延期审批");
	static readonly CASE_MANAGE_CLOSE = new EnumItem(3, "案管办结");
}

const approvalStatusOptions = [
  { label: '待审核', value: '1' },
  { label: '审核中', value: '5' },
  { label: '审核完成', value: '10' }
]

const approvalResultOptions = [
  { label: '通过', value: '1' },
  { label: '失败', value: '5' }
]

export default {
  approvalStatusOptions,
  approvalResultOptions,
  approvalTypeOptions,
	ApprovalStatusEnum
}
