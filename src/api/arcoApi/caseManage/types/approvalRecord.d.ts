declare interface REQUEST_POST_APPROVAL_SAVE_PARAM_TYPE {
  caseId: string | number
  successReason: string
  caseCloseTime: string
  caseDelayTime: string
  approvalType: string
  closeReason: string
  delayReason: string
	suspendReason:string
  mdtResult: string
}

declare interface REQUEST_POST_BATCH_APPROVAL_START_PARAM_TYPE {
	caseId: string | number
	successReason: string
	caseCloseTime: string
	caseDelayTime: string
	approvalType: string
	closeReason: string
	delayReason: string
	mdtResult: string,
	mediateCasePageBO: OBJ_KEY_STR_ANY_TYPE
	caseIdList: (string | number)[]
	ifChooseAll: boolean
}

declare interface REQUEST_POST_APPROVAL_AUDIT_PARAM_TYPE {
  approvalIdList: (string | number)[]
  auditDes: string
  auditResult: string
  caseDelayTime: string
}

declare interface REQUEST_POST_APPROVAL_LISTSEARCH_PARAM_TYPE {
  approvalResult: string
  approvalStatus: string
  approvalType: string
  caseNo: string
  endTime: string
  startOrgIdList: number[]
  startTime: string
  allTime?: string[]
}

declare interface REQUEST_POST_APPROVAL_LIST_PARAM_TYPE {
  param?: REQUEST_POST_APPROVAL_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_POST_APPROVAL_LIST_DATA_TYPE {
  approvalId: number | null
  approvalResult: string
  approvalStatus: string
  approvalType: string
  caseCloseTime: string
  caseId: number | null
  caseNo: string
  closeReason: string
  delayReason: string
  disabled: boolean
  firstAuditDes: string
  firstAuditResult: string
  mdtResult: string
  secondAuditDes: string
  secondAuditResult: string
  startManName: string
  startManRole: string
  startOrgName: string
  startTime: number
}
