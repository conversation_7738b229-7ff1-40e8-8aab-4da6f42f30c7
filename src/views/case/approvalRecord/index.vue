<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchForm"
            :label-col-props="{ span: 8 }"
            :wrapper-col-props="{ span: 16 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="caseNo" label="案件编号">
                  <a-input v-model="searchForm.caseNo" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="approvalType" label="审批类型">
                  <a-select
                    v-model="searchForm.approvalType"
                    :options="approvalTypeOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="approvalStatus" label="审核状态">
                  <a-select
                    v-model="searchForm.approvalStatus"
                    :options="approvalStatusOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="startOrgIdList" label="发起组织">
                  <a-select
                    v-model="searchForm.startOrgIdList"
                    :options="mdtOrgsOptions"
                    placeholder="请选择"
                    allow-clear
                    multiple
                    :max-tag-count="1"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="startTime" label="申请时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="approvalResult" label="审核结果">
                  <a-select
                    v-model="searchForm.approvalResult"
                    :options="mediateResultOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row style="margin-bottom: 16px">
        <a-col v-auth="['caseApprovalProcess']" :span="24" class="mdt-col-flexend">
          <a-space :size="10">
            <a-button type="primary" @click="handleBatchOperation(1)">批量通过</a-button>
            <a-button @click="handleBatchOperation(0)">批量拒绝</a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="approvalId"
        :loading="loading"
        :pagination="pagination"
        :columns="cloneColumns"
        :data="renderData"
        :bordered="false"
        :pageize="size"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @selection-change="handleTableSelect"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #approvalType="{ record }">
          {{ approvalTypeObj[record.approvalType] || record.approvalType }}
        </template>
        <template #approvalStatus="{ record }">
          {{ approvalStatusObj[record.approvalStatus] || record.approvalStatus }}
        </template>
        <template #mdtResult="{ record }">
          {{ mediateResultObj[record.mdtResult] || record.mdtResult }}
        </template>
        <template #operations="{ record }">
          <a-space v-auth="['caseApprovalProcess']">
            <a-button :disabled="record.disabled" type="text" @click="handleAudit(record, '1')"> 通过 </a-button>
            <a-button :disabled="record.disabled" type="text" @click="handleAudit(record, '2')"> 拒绝 </a-button>
          </a-space>
        </template>
      </a-table>
      <AuditApprovalModal
        v-model:visible="modalVisible"
        :approval-id-list="selectedIds"
        :mdt-result="modalType"
        :title="modalTitle"
        @comfirm="handleComfirm"
        @cancel="handleCancel"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'

import { getCaseApprovalPageList } from '@/api/arcoApi/caseManage/approvalRecord'
import { getAllMdtOrg } from '@/api/arcoApi/businessConf/mediateOrgInfoManage'
import AuditApprovalModal from './components/audit-modal.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, ref, reactive, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/caseManage'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const generateFormModel = () => {
  return {
    startOrgIdList: [],
    approvalResult: '',
    approvalStatus: '',
    approvalType: '',
    caseNo: '',
    endTime: '',
    startTime: '',
    allTime: []
  }
}

const { loading, setLoading } = useLoading(true)
const modalType = ref<'1' | '2'>('1')
const pageZhName = ref('结案审核')
const router = useRouter()
const route = useRoute()

const searchForm = ref<REQUEST_POST_APPROVAL_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_POST_APPROVAL_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const selectedIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const modalVisible = ref(false)

const query = route.query

if (query && Object.keys(query).length > 0) {
  searchForm.value = { ...searchForm.value, ...query }
}

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const mediateResultOptions = ref<SelectOptionData[]>(dict.mediateResultOptions)
const approvalStatusOptions = ref<SelectOptionData[]>(dict.auditStatusOptions)
const approvalTypeOptions = ref<SelectOptionData[]>(dict.approvalTypeOptions)

const mdtOrgsOptions = ref<SelectOptionData[]>([])
const pagination = reactive({ ...basePagination })

const approvalStatusObj = computed(() => {
  return dictEnumValToObject(dict.auditStatusOptions)
})

const mediateResultObj = computed(() => {
  return dictEnumValToObject(dict.mediateResultOptions)
})

const approvalTypeObj = computed(() => {
  return dictEnumValToObject(dict.approvalTypeOptions)
})

const modalTitle = computed(() => {
  return `${modalType.value === '1' ? '通过' : '拒绝'}`
})

const columns = computed<TableColumnData[]>(() => [
  { width: 240, tooltip: true, ellipsis: true, fixed: 'left', title: '案件编号', dataIndex: 'caseNo' },
  { width: 120, title: '审批类型', dataIndex: 'approvalType', slotName: 'approvalType' },
  { width: 120, tooltip: true, ellipsis: true, title: '发起人', dataIndex: 'startManName' },
  { width: 120, tooltip: true, ellipsis: true, title: '发起人角色', dataIndex: 'startManRole' },
  { width: 120, tooltip: true, ellipsis: true, title: '发起组织', dataIndex: 'startOrgName' },
  { width: 120, title: '审核状态', dataIndex: 'approvalStatus', slotName: 'approvalStatus' },
  { width: 150, tooltip: true, ellipsis: true, title: '审核结果', dataIndex: 'approvalResult' },
  { width: 200, tooltip: true, ellipsis: true, title: '审核备注', dataIndex: 'firstAuditDes' },
  { width: 200, tooltip: true, ellipsis: true, title: '申请时间', align: 'center', dataIndex: 'startTime' },
  { width: 200, title: '调解结果', dataIndex: 'mdtResult', slotName: 'mdtResult' },
  { width: 200, tooltip: true, ellipsis: true, title: '办结日期', dataIndex: 'caseCloseTime' },
  { width: 200, tooltip: true, ellipsis: true, title: '延期理由', dataIndex: 'delayReason' },
  { width: 160, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const handleBatchOperation = (type: 1 | 0) => {
  if (selectedIds.value && selectedIds.value.length) {
    modalType.value = type ? '1' : '2'
    modalVisible.value = true
  } else {
    Message.warning('请选择需要审核的案件')
  }
}

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const handleTableSelect = (val: (string | number)[]) => {
  selectedIds.value = val
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.startTime = timeArr[0] || ''
    searchForm.value.endTime = timeArr[1] || ''
  } else {
    searchForm.value.startTime = ''
    searchForm.value.endTime = ''
  }
}

const handleAudit = (row: REQUEST_POST_APPROVAL_LIST_DATA_TYPE, type: '1' | '2') => {
  if (row.approvalId) {
    selectedIds.value = [row.approvalId]
    modalType.value = type
    modalVisible.value = true
  }
}

const handleComfirm = () => {
  modalVisible.value = false
  getTableData()
}
const handleCancel = () => {
  modalVisible.value = false
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getCaseApprovalPageList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
const getAllMdtOrgData = () => {
  getAllMdtOrg().then((res) => {
    if (res && res.length) {
      mdtOrgsOptions.value = res.map((item) => ({ label: item.orgName, value: item.orgId }))
    }
  })
}

getAllMdtOrgData()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
