<template>
  <div class="case-list-page">
    <a-card class="case-list-card" :bordered="false">
      <template #title>
        <span class="bold">案件审批管理</span>
      </template>
      <div style="overflow: hidden">
        <a-row>
          <a-col :flex="1">
            <a-form
              :model="searchForm"
              :label-col-props="{ span: 6 }"
              :wrapper-col-props="{ span: 18 }"
              label-align="left"
            >
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item field="caseNo" label="案件编号">
                    <a-input v-model="searchForm.caseNo" placeholder="请输入内容，支持模糊检索" allow-clear />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="approvalType" label="审批类型">
                    <a-select
                      v-model="searchForm.approvalType"
                      :options="approvalTypeOptions"
                      placeholder="请选择"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="approvalStatus" label="审核状态">
                    <a-select
                      v-model="searchForm.approvalStatus"
                      :options="approvalStatusOptions"
                      placeholder="请选择"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="startOrgIdList" label="发起组织">
                    <!-- <a-select
                      v-model="searchForm.startOrgIdList"
                      :options="entrustsOptions"
                      :max-tag-count="1"
                      placeholder="请选择"
                      allow-clear
                      multiple
                    /> -->
                    <a-cascader
                      v-model="searchForm.startOrgIdList"
                      :options="cascaderEntrustOptions"
                      :max-tag-count="1"
                      placeholder="请选择"
                      allow-search
                      allow-clear
                      multiple
                      @change="handleCascaderEntrusts"
                    ></a-cascader>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="allTime" label="申请时间">
                    <a-range-picker
                      v-model="searchForm.allTime"
                      show-time
                      :time-picker-props="{ defaultValue: ['00:00:00', '00:00:00'] }"
                      style="width: 100%"
                      @change="handlePickerSelect"
                    >
                      <template #suffix-icon><icon-schedule /></template>
                    </a-range-picker>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="approvalResult" label="审批结果">
                    <a-select
                      v-model="searchForm.approvalResult"
                      :options="approvalResultOptions"
                      placeholder="请选择"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-col>
          <a-divider style="height: 84px" direction="vertical" />
          <a-col :flex="'86px'" style="text-align: right">
            <a-space direction="vertical" :size="18">
              <a-button type="primary" @click="search">
                <template #icon> <icon-search /> </template>查询
              </a-button>
              <a-button @click="reset">
                <template #icon> <icon-refresh /> </template> 重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <a-divider style="margin-top: 0; margin-bottom: 16px" />
        <a-row style="margin-bottom: 16px">
          <a-col v-auth="['caseApprovalProcess']" :span="24">
            <a-button type="primary" @click="handleBatchPass">批量通过</a-button>
            <a-button type="primary" status="danger" class="ml12" @click="handleBatchReject">批量拒绝</a-button>
          </a-col>
        </a-row>
      </div>
      <a-table
        v-model:selected-keys="selectedKeys"
        row-key="approvalId"
        :loading="loading"
        :pagination="pagination"
        :columns="columns"
        :data="renderData"
        :pageize="size"
        :row-selection="rowSelection"
        :scroll="{ x: '100%', y: '100%' }"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #approvalType="{ record }">
          {{ approvalTypeObj[record.approvalType] || record.approvalType }}
        </template>
        <template #approvalStatus="{ record }">
          {{ approvalStatusObj[record.approvalStatus] || record.approvalStatus }}
        </template>
        <template #mdtResult="{ record }">
          {{ mediateResultObj[record.mdtResult] || record.mdtResult }}
          <span v-if="record.successReason">
            【{{ successReasonsObj[record.successReason] || record.successReason }}】
          </span>
          <span v-if="record.closeReason"> 【{{ dictStore.getDictTagByTypeAndKey(DictTypeEnum.case_close_reason,record.closeReason)}}】 </span>
          <span v-if="record.suspendReason"> 【{{ dictStore.getDictTagByTypeAndKey(DictTypeEnum.case_suspension_of_mediation,record.suspendReason)}}】 </span>
        </template>

        <template #firstAuditResult="{ record }">
          <span v-if="record.firstAuditResult === '1'">通过</span>
          <span v-else-if="record.firstAuditResult === '5'">失败</span>
          <span v-else>{{ record.firstAuditResult || '无' }}</span>
        </template>
        <template #secondAuditResult="{ record }">
          <span v-if="record.secondAuditResult === '1'">通过</span>
          <span v-else-if="record.secondAuditResult === '5'">失败</span>
          <span v-else>{{ record.secondAuditResult || '无' }}</span>
        </template>
        <template #operations="{ record }">
          <a-space v-auth="['caseApprovalProcess']">
            <a-button v-if="!record.disabled" type="text" size="mini" @click="handleCasePass(record)">通过</a-button>
            <a-button v-if="!record.disabled" type="text" status="danger" size="mini" @click="handleCaseReject(record)">
              拒绝
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>
    <a-modal
      width="550px"
      :title="auditInfoForm.approvalIdList.length > 1 ? '批量通过' : '通过'"
      :visible="passVisible"
      @ok="handleBatchPassConfirm"
      @cancel="handleBatchPassCancel"
    >
      <a-form
        ref="auditFormRef"
        :model="auditInfoForm"
        :rules="auditFormRules"
        :label-col-props="{ span: 7 }"
        :wrapper-col-props="{ span: 17 }"
        label-align="left"
      >
        <a-form-item v-if="showTime" label="是否需要调整时间">
          <a-radio-group v-model="hasShowDelayTime">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="hasShowDelayTime && showTime" field="caseDelayTime" label="延期截止日期">
          <a-date-picker v-model="auditInfoForm.caseDelayTime" style="width: 100%">
            <template #suffix-icon><icon-schedule /></template>
          </a-date-picker>
        </a-form-item>
        <a-form-item field="auditDes" label="通过原因">
          <a-textarea
            v-model="auditInfoForm.auditDes"
            :auto-size="{ minRows: 4 }"
            placeholder="请输入通过原因"
            allow-clear
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      width="500px"
      :title="auditInfoForm.approvalIdList.length > 1 ? '批量拒绝' : '拒绝'"
      :visible="rejectVisible"
      @ok="handleBatchRejectConfirm"
      @cancel="handleBatchRejectCancel"
    >
      <a-form ref="auditFormRef" :model="auditInfoForm" :rules="auditFormRules" label-align="left">
        <a-form-item field="auditDes" label="拒绝原因">
          <a-textarea
            v-model="auditInfoForm.auditDes"
            :auto-size="{ minRows: 4 }"
            placeholder="请输入拒绝原因"
            allow-clear
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import { pageList, batchAuditApproval } from '@/api/arcoApi/caseManage/caseAuditManage'
import type { CascaderOption } from '@arco-design/web-vue/es/cascader/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import { getAuditApprovalType } from '@/api/arcoApi/caseManage/caseAuditManage'
import { findOrgStructure } from '@/api/eleApi/case/batchManage'
import { FormInstance, Message } from '@arco-design/web-vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { useRouter, useRoute } from 'vue-router'
import { removeNullParam } from '@/utils/index'
import { dictEnumValToObject } from '@/utils'
import { computed, ref, reactive } from 'vue'
import dict from '@/dict/caseManage'
import _ from 'lodash'
import { DictTypeEnum } from '@/dict/systemManage.ts'
import { useDictStore } from '@arco/store'
const dictStore = useDictStore()

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
const searchFormModel = () => {
  return {
    startOrgIdList: [], // 仅作为数据回显，不参与列表数据筛选
    approvalResult: '',
    approvalStatus: '',
    approvalType: '',
    orgBOList: [],
    startTime: '',
    endTime: '',
    caseNo: '',
    allTime: []
  }
}

const auditFormModel = () => {
  return {
    approvalIdList: [],
    caseDelayTime: '',
    auditResult: '',
    auditDes: ''
  }
}

const searchForm = ref<REQUEST_POST_AUDIT_LISTSEARCH_PARAM_TYPE>(searchFormModel())
const auditInfoForm = ref<REQUEST_POST_AUDIT_PARAM_TYPE>(auditFormModel())
const renderData = ref<REQUEST_POST_AUDIT_LIST_DATA_TYPE[]>([])
const { loading, setLoading } = useLoading(true)

const selectedKeys = ref<(string | number)[]>([])
const size = ref<SizeProps>('medium')
const router = useRouter()
const route = useRoute()
const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const auditFormRules = ref({ auditDes: [{ required: true, message: '请输入审核原因', trigger: 'blur' }] })
const cascaderEntrustOptions = ref<CascaderOption[]>([])
const auditFormRef = ref<FormInstance>()
const hasShowDelayTime = ref(0)
const showTime = ref(false)
const query = route.query

if (query && Object.keys(query).length > 0) {
  searchForm.value = { ...searchForm.value, ...query }
}

const rowSelection: TableRowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false
})

const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 240, tooltip: true, ellipsis: true, fixed: 'left', title: '案件编号', slotName: 'caseNo' },
  { width: 120, tooltip: true, ellipsis: true, title: '审批类型', dataIndex: 'approvalType', slotName: 'approvalType' },
  { width: 170, tooltip: true, ellipsis: true, title: '调解结果', align: 'center', slotName: 'mdtResult' },
  { width: 180, tooltip: true, ellipsis: true, title: '办结日期', dataIndex: 'caseCloseTime' },
  { width: 180, tooltip: true, ellipsis: true, title: '延期截止日期', dataIndex: 'caseDelayTime' },
  { width: 120, tooltip: true, ellipsis: true, title: '延期理由', dataIndex: 'delayReason' },
  { width: 120, tooltip: true, ellipsis: true, title: '发起人', dataIndex: 'startManName' },
  { width: 120, tooltip: true, ellipsis: true, title: '发起人角色', dataIndex: 'startManRole' },
  { width: 120, tooltip: true, ellipsis: true, title: '发起组织', dataIndex: 'startOrgName' },
  { width: 140, title: '审核状态', dataIndex: 'approvalStatus', slotName: 'approvalStatus' },
  { width: 150, title: '组织内部审核结果', dataIndex: 'firstAuditResult', slotName: 'firstAuditResult' },
  { width: 150, tooltip: true, ellipsis: true, title: '组织内部审核备注', dataIndex: 'firstAuditDes' },
  { width: 150, title: '案源方审核结果', dataIndex: 'secondAuditResult', slotName: 'secondAuditResult' },
  { width: 150, tooltip: true, ellipsis: true, title: '案源方审核备注', dataIndex: 'secondAuditDes' },
  { width: 170, ellipsis: true, title: '申请时间', dataIndex: 'startTime' },
  { width: 130, align: 'center', fixed: 'right', title: '操作', dataIndex: 'operations', slotName: 'operations' }
])

const approvalResultOptions = ref<SelectOptionData[]>(dict.approvalResultOptions)
const approvalStatusOptions = ref<SelectOptionData[]>(dict.auditStatusOptions)
const approvalTypeOptions = ref<SelectOptionData[]>(dict.approvalTypeOptions)

const approvalStatusObj = computed(() => {
  return dictEnumValToObject(dict.auditStatusOptions)
})

const mediateResultObj = computed(() => {
  return dictEnumValToObject(dict.mediateResultOptions)
})

const approvalTypeObj = computed(() => {
  return dictEnumValToObject(dict.approvalTypeOptions)
})

const closeReasonsObj = computed(() => {
  return dictEnumValToObject(dict.closeReasonsCascaderOptions)
})

const successReasonsObj = computed(() => {
  return dictEnumValToObject(dict.successReasonOptions)
})

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const handleCascaderEntrusts = (value: any) => {
  if (value && value.length) {
    searchForm.value.orgBOList = value.map((key) => {
      let keys: string[] = key.split('-')
      return { type: keys[0], orgId: keys[1] }
    })
  } else {
    searchForm.value.orgBOList = []
  }
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.startTime = timeArr[0] || ''
    searchForm.value.endTime = timeArr[1] || ''
  } else {
    searchForm.value.startTime = ''
    searchForm.value.endTime = ''
  }
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    let params = removeNullParam({ ...searchForm.value }, 'startOrgIdList')
    const data = await pageList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: params
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const search = () => {
  pagination.current = 1
  selectedKeys.value = []
  getTableData({ ...basePagination })
}
const reset = () => {
  searchForm.value = searchFormModel()
}
const onPageChange = (current: number) => {
  selectedKeys.value = []
  getTableData({ ...pagination, current })
}
const onPageSizeChange = (pageSize: number) => {
  selectedKeys.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}
const getAllEntrusts = () => {
  findOrgStructure().then((res: any) => {
    console.log(res)
    if (res && res.length) {
      cascaderEntrustOptions.value = res.map((orgItem) => {
        let entrustRow = {
          value: `${orgItem.type}-${orgItem.orgId}`,
          label: orgItem.orgName,
          children: []
        }
        if (orgItem.orgStructureDeptDTOList) {
          entrustRow.children = orgItem.orgStructureDeptDTOList.map((deptItem) => {
            let depttRow = {
              value: `${deptItem.type}-${deptItem.deptId}`,
              label: deptItem.deptName,
              children: []
            }
            if (deptItem.entrustsMediatorDTOList) {
              depttRow.children = deptItem.entrustsMediatorDTOList.map((mediatorItem) => ({
                value: `${mediatorItem.type}-${mediatorItem.mediatorId}`,
                label: mediatorItem.mediatorName
              }))
            }
            return depttRow
          })
        }
        return entrustRow
      })
    }
  })
}
getAllEntrusts()
getTableData()
// 批量通过
const passVisible = ref<boolean>(false)
const handleBatchPass = async () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  let approvalIdList = selectedKeys.value
  showTime.value = await getAuditApprovalType(approvalIdList)
  auditInfoForm.value.approvalIdList = approvalIdList
  passVisible.value = true
}
const handleBatchPassConfirm = async () => {
  const state = await auditFormRef.value?.validate()
  if (!state) {
    let params = { ...auditInfoForm.value, auditResult: '1' }
    await batchAuditApproval(params)
    Message.success('操作成功')
    handleBatchPassCancel()
    search()
  }
}
const handleBatchPassCancel = () => {
  passVisible.value = false
  auditInfoForm.value = auditFormModel()
}
// 批量拒绝
const rejectVisible = ref<boolean>(false)
const handleBatchReject = () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  auditInfoForm.value.approvalIdList = selectedKeys.value
  rejectVisible.value = true
}
const handleBatchRejectConfirm = async () => {
  const state = await auditFormRef.value?.validate()
  if (!state) {
    let params = { ...auditInfoForm.value, auditResult: '5' }
    await batchAuditApproval(params)
    Message.success('操作成功')
    handleBatchRejectCancel()
    search()
  }
}
const handleBatchRejectCancel = () => {
  rejectVisible.value = false
  auditInfoForm.value = auditFormModel()
}
// 通过或拒绝
const handleCasePass = async (row: REQUEST_POST_AUDIT_LIST_DATA_TYPE) => {
  let approvalIdList = [row.approvalId]
  showTime.value = await getAuditApprovalType(approvalIdList)
  auditInfoForm.value.approvalIdList = approvalIdList
  passVisible.value = true
}
const handleCaseReject = (row: REQUEST_POST_AUDIT_LIST_DATA_TYPE) => {
  auditInfoForm.value.approvalIdList = [row.approvalId]
  rejectVisible.value = true
}
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
.modal-textarea {
  :deep(.arco-textarea) {
    min-height: 120px;
  }
}
</style>
