import { Ref,ref } from 'vue'

// 案件上下文类型定义
export interface CaseContext {
  // 案件ID
  caseId: string;
  
  // 案件基本信息
  caseBasic: Ref<REQUEST_CASEBASIS_DATA_TYPE>;
  
  // 当事人列表
	litigantList:Ref<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]>;
  
  // 可以根据需要添加更多的上下文数据
  // 例如：调解记录、任务列表等
}

// 创建默认的空上下文
export const createDefaultCaseContext = (): CaseContext => ({
  caseId: '',
  caseBasic: ref<REQUEST_CASEBASIS_DATA_TYPE>({
		caseId: 0,
		caseNo: '',
		entrustsName: '',
		entrustsDeptId: 0,
		entrustsDeptName: '',
		creatorName: '',
		mdtCaseStatus: '',
		mediateStatus: '',
		mediateResult: '',
		orgName: '',
		deptName: '',
		currentMediatorName: '',
		remainingDays: 0,
		createTime: '',
		LocalDateTime: '',
		closeTime: '',
		caseName: '',
		mediateBeginTime: '',
		mediateDoneTime: '',
		mediateCloseTime: '',
		caseApplyTime: '',
		caseNatureContent: '',
		expirationTime: '',
		defendAntLitigantNames: '',
		plainTiffLitigantNames: '',
		otherDemand: '',
		successReason: '',
		closeReason: '',
		businessType: ''
	}),
  litigantList:ref<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]>([])
})

// 创建上下文的 Symbol key，避免命名冲突
export const CaseContextKey = Symbol('CaseContext')
