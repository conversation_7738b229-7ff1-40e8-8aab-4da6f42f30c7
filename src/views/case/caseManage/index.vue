<template>
  <div ref="caseList" class="case-list-page">
    <a-card class="case-list-card" :bordered="false">
      <template #title>
        <span class="bold">案件查询</span>
      </template>
      <template #extra>
        <a-space :size="18">
          <a-button type="text" size="mini" @click="customModuleModalVisible = true">
            更多案件自定义信息 <icon-double-down class="f16" />
          </a-button>

          <a-button type="text" size="mini" @click="handleHighSearch">
            更多案件固定信息 <icon-double-down class="f16" />
          </a-button>

          <a-button @click="reset">
            <template #icon> <icon-refresh /> </template> 重置
          </a-button>
          <a-button type="primary" @click="search">
            <template #icon> <icon-search /> </template>查询
          </a-button>
        </a-space>
      </template>
      <div ref="caseListHeader">
        <a-row>
          <a-col :flex="1">
            <a-form
              layout="vertical"
              :model="searchForm"
              :label-col-props="{ span: 6 }"
              :wrapper-col-props="{ span: 18 }"
              label-align="left"
            >
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item field="caseNo" label="案件编号" extra="多个用换行符隔开(<=1000行)" @keydown.stop>
                    <a-textarea
                      v-model="searchForm.caseNo"
                      :class="{ 'height-32': countNewLines(searchForm.caseNo) <= 1 }"
                      placeholder="请输入内容，支持模糊检索"
                      allow-clear
                      :max-length="1000"
                      show-word-limit
                      :word-length="countNewLines"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="mdtCaseStatuses" label="案件状态">
                    <a-select
                      v-model="searchForm.mdtCaseStatuses"
                      :options="mdtCaseStatusOptions"
                      :max-tag-count="1"
                      placeholder="请选择"
                      allow-clear
                      multiple
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="mediateStatuses" label="调解状态">
										<DictDataSelect v-model="searchForm.mediateStatuses" :dict-type="DictTypeEnum.mediate_status"  checkStrictly multiple />
									</a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="cascaderEntrustsIds" label="案源方">
                    <a-cascader
                      v-model="searchForm.cascaderEntrustsIds"
                      :options="cascaderEntrustOptions"
                      :max-tag-count="1"
                      placeholder="请选择"
                      allow-search
                      allow-clear
                      multiple
                      @change="handleCascaderEntrusts"
                    ></a-cascader>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="mediateResult" label="调解结果">
                    <a-select
                      v-model="searchForm.mediateResult"
                      :options="mediateResultOptions"
                      placeholder="请选择"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="createTime" label="创建时间">
                    <a-range-picker
                      v-model="searchForm.createTime"
                      show-time
                      :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
                      class="percent-100"
                      @change="handleCreateTimeSelect"
                    ></a-range-picker>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-col>
        </a-row>
        <custom-module-search ref="CustomModuleSearchRef" v-model:visible="customModuleModalVisible" />
        <a-divider style="margin-top: 0; margin-bottom: 16px" />
        <a-row style="margin-bottom: 16px">
          <a-col :span="8">
            <a-button type="primary">
              <a-space :size="10">
                <a-checkbox v-model="ifChooseAll" :disabled="!renderData.length" @change="handleAllSelect" />
                <span>选择全部</span>
              </a-space>
            </a-button>
            <span v-if="ifChooseAll" class="ml10">已选 {{ pagination.total }} 件</span>
          </a-col>
          <a-col :span="16" class="mdt-col-flexend">
            <a-popover
              content-class="mdt-popover padding0"
              popup-container=".custom-popover-btn"
              trigger="click"
              position="bottom"
            >
              <a-button class="custom-popover-btn" type="primary">更多操作 <icon-down /></a-button>
              <template #content>
                <a-scrollbar type="track" style="max-height: 150px; overflow-y: scroll">
                  <ul class="mdt-popover-ul">
                    <li v-auth="['caseCreate']" class="mdt-popover-li" @click="handleCaseCreate">案件创建</li>
                    <li v-auth="['caseImport']" class="mdt-popover-li" @click="handleCaseImport">案件导入</li>
                    <li v-auth="['caseUpdate']" class="mdt-popover-li" @click="handleCaseUpdate">案件更新</li>
                    <li v-auth="['caseExport']" class="mdt-popover-li" @click="handleCaseExport">案件导出</li>
                    <li
                      v-auth="['caseDistribute']"
                      v-rule="needRule || ['调解组织管理员', '调解组长']"
                      class="mdt-popover-li"
                      @click="handleCaseAssign"
                    >
                      案件分派
                    </li>
                    <li v-auth="['caseTaskAssign']" class="mdt-popover-li" @click="handleBatchTaskRegister">任务分派</li>

                    <li v-auth="['phoneCheck']" class="mdt-popover-li" @click="handleBatchNumberCheck">批量号码检测</li>
                    <li v-auth="['connectionRepair']" class="mdt-popover-li" @click="handleBatchCallRepair(0)">
                      批量失联修复
                    </li>
                    <li v-auth="['smsSend']" class="mdt-popover-li" @click="handleBatchSmsSend">批量短信发送</li>
                    <li v-auth="['batchGenerateDoc']" class="mdt-popover-li" @click="handleBatchDocGenerate">
                      批量生成文书
                    </li>
                    <li v-auth="['batchApproval']" class="mdt-popover-li" @click="handleBatchApproval">批量办结申请</li>
                    <li v-auth="['connectionRepair']" class="mdt-popover-li" @click="handleBatchCallRepair(1)">
                      新批量失联修复
                    </li>
                  </ul>
                </a-scrollbar>
              </template>
            </a-popover>
          </a-col>
        </a-row>
      </div>
      <div ref="caseListTable">
        <vxe-grid ref="gridRef" v-bind="gridOptions" :data="renderData" v-on="gridEvents">
          <template #toolbarButtons>
            <!-- 可以添加工具栏内容 -->
          </template>
					<template #caseNo="{ row }">
            <a-link @click="handleToCaseDetails(row.caseId)" >{{row.caseNo}}</a-link>
          </template>
          <template #nextDoTime="{ row }">
            {{ timeToDateStr(row.nextDoTime) }}
          </template>
          <template #caseMediateType="{ row }">
            {{ caseMediateTypeObj[row.caseMediateType] || row.caseMediateType }}
          </template>
          <template #mdtCaseStatus="{ row }">
            {{ mdtCaseStatusObj[row.mdtCaseStatus] || row.mdtCaseStatus }}
          </template>
          <template #mediateStatus="{ row }">
						{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.mediate_status,row.mediateStatus)}}
          </template>
          <template #mediateResult="{ row }">
            {{ mediateResultObj[row.mediateResult] || row.mediateResult }}
            <span v-if="row.successReason">
           【{{ dictStore.getDictTagByTypeAndKey(DictTypeEnum.case_success_reason,row.successReason)}}】
            </span>
            <span v-if="row.closeReason"> 【{{ dictStore.getDictTagByTypeAndKey(DictTypeEnum.case_close_reason,row.closeReason) }}】 </span>
						<span v-if="row.suspendReason"> 【{{ dictStore.getDictTagByTypeAndKey(DictTypeEnum.case_suspend_reason, row.suspendReason)
							}}】 </span>
          </template>
					<template #caseManageCloseReason="{ row }">
						{{ dictStore.getDictTagByTypeAndKey(DictTypeEnum.case_manage_close_reason,row.caseManageCloseReason) }}
					</template>
          <template #remainingDays="{ row }">
            <span v-if="row.remainingDays" class="danger">{{ row.remainingDays }} 天</span>
            <span v-else>-- --</span>
          </template>
          <template #caseApplyTime="{ row }">{{ timeToDateStr(row.caseApplyTime) }}</template>
          <template #mediateBeginTime="{ row }">{{ timeToDateStr(row.mediateBeginTime) }}</template>
          <template #expirationTime="{ row }">{{ timeToDateStr(row.expirationTime) }}</template>
          <template #closeTime="{ row }">{{ timeToDateStr(row.closeTime) }}</template>

          <template #operations="{ row }">
            <a-button v-auth="['caseView']" type="text" size="mini" @click="handleToCaseDetails(row.caseId)">
              详情
            </a-button>
          </template>
        </vxe-grid>
      </div>
    </a-card>
    <a-drawer :width="480" title="高级查询" :visible="visible" @cancel="visible = false">
      <a-form
        ref="highSearchFormRef"
        :model="highSearchForm"
        :label-col-props="{ span: 7 }"
        :wrapper-col-props="{ span: 17 }"
        label-align="right"
      >
        <div class="form-title">案件基本信息</div>
        <a-form-item field="applyTime" label="案件申请时间">
          <a-range-picker
            v-model="highSearchForm.applyTime"
            :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
            class="percent-100"
            show-time
            @change="handleApplyTimeChange"
          ></a-range-picker>
        </a-form-item>
        <a-form-item field="caseStartTime" label="调解开始时间">
          <a-range-picker
            v-model="highSearchForm.caseStartTime"
            :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
            class="percent-100"
            show-time
            @change="handleCaseStartTimeChange"
          ></a-range-picker>
        </a-form-item>
        <a-form-item field="caseExpiraTime" label="调解截止时间">
          <a-range-picker
            v-model="highSearchForm.caseExpiraTime"
            :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
            class="percent-100"
            show-time
            @change="handleCaseExpiraTimeChange"
          ></a-range-picker>
        </a-form-item>
        <a-form-item field="mediateCloseTime" label="案件关闭时间">
          <a-range-picker
            v-model="highSearchForm.mediateCloseTime"
            :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
            class="percent-100"
            show-time
            @change="handleCaseCloseTimeChange"
          ></a-range-picker>
        </a-form-item>
        <a-form-item field="caseNatureIds" label="案由">
          <dict-data-select v-model="highSearchForm.caseNatureIds" :dict-type="DictTypeEnum.NATURE" :multiple="true" :model-value-witch="'id'" :check-strictly="true" />
        </a-form-item>
        <a-form-item field="caseMediateTypes" label="调解类型">
          <a-select
            v-model="highSearchForm.caseMediateTypes"
            :options="dict.caseMediateTypeOptions"
            allow-search
            allow-clear
            multiple
            placeholder="请选择"
          />
        </a-form-item>
        <!-- :rules="[{ required: true, message: '请选择导入模板' }]" -->
        <a-form-item field="expirationTimeStart" label="剩余调解时效(天)">
          <a-space :size="10">
            <a-input-number v-model="highSearchForm.expirationTimeStart" :min="-100" :max="100" placeholder="请输入" />
            至
            <a-input-number v-model="highSearchForm.expirationTimeEnd" :min="-100" :max="100" placeholder="请输入" />
            <!-- <a-popover>
              <icon-info-circle-fill :size="20" style="color: rgb(255, 100, 22)"></icon-info-circle-fill>
              <template #content> 剩余调解时效(天)，最大天数不能超过100天，最小天数不能小于-100天 </template>
            </a-popover> -->
          </a-space>
        </a-form-item>
        <div class="form-title">案件分配情况</div>
        <a-form-item field="orgId" label="调解组织">
          <a-select
            v-model="highSearchForm.orgIds"
            :options="orgNameOptions"
            placeholder="请选择"
            allow-clear
            multiple
            :max-tag-count="1"
            @change="handleMediationOrganizationChange"
          />
        </a-form-item>
        <a-form-item field="deptId" label="调解团队">
          <a-select
            v-model="highSearchForm.deptIds"
            :options="deptNameOptions"
            placeholder="请选择"
            allow-clear
            multiple
            :max-tag-count="1"
            :disabled="!highSearchForm.orgIds || !highSearchForm.orgIds.length"
            @change="handleMediationTeamChange"
          />
        </a-form-item>
        <a-form-item field="currentMediatorIds" label="调解员">
          <a-select
            v-model="highSearchForm.currentMediatorIds"
            :options="currentMediatorNameOptions"
            :max-tag-count="1"

            placeholder="请选择"
            allow-clear
            multiple
          />
        </a-form-item>
        <div class="form-title">案件办理情况</div>
        <a-form-item field="closeTime" label="调解办结时间">
          <a-range-picker
            v-model="highSearchForm.closeTime"
            :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
            show-time
            class="percent-100"
            @change="handleCloseTimeSelect"
          ></a-range-picker>
        </a-form-item>
				<a-form-item field="closeTime" label="案管办结时间">
					<a-range-picker
						v-model="highSearchForm.caseManageTime"
						:time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
						show-time
						class="percent-100"
						@change="handleCaseManageCloseTimeSelect"
					></a-range-picker>
				</a-form-item>
        <a-form-item field="businessType" label="业务类型">
					<BusinessTypeSelect v-model="highSearchForm.businessTypes" multiple />
				</a-form-item>
        <a-form-item field="closeReasons" label="终止原因">
					<dict-data-select group-by-business-type v-model="highSearchForm.closeReasons" :dict-type="DictTypeEnum.case_close_reason" multiple :filter-business-types="highSearchForm.businessTypes" />
        </a-form-item>
        <a-form-item field="successReasons" label="结案方式">
					<dict-data-select group-by-business-type v-model="highSearchForm.successReasons" :dict-type="DictTypeEnum.case_success_reason" multiple :filter-business-types="highSearchForm.businessTypes" />
        </a-form-item>
				<a-form-item field="suspendReasons" label="中止原因">
					<dict-data-select group-by-business-type v-model="highSearchForm.suspendReasons" :dict-type="DictTypeEnum.case_suspend_reason" multiple :filter-business-types="highSearchForm.businessTypes" />
				</a-form-item>
				<a-form-item field="caseManageCloseReason" label="案管办结结果">
					<dict-data-select group-by-business-type v-model="highSearchForm.caseManageCloseReasons" :dict-type="DictTypeEnum.case_manage_close_reason" multiple :filter-business-types="highSearchForm.businessTypes" />
				</a-form-item>
        <div class="form-title">当事人信息</div>
        <a-form-item field="litigantName" label="当事人姓名">
          <a-input v-model="highSearchForm.litigantName" placeholder="请输入内容" allow-clear />
        </a-form-item>
        <a-form-item field="litigantPhone" label="联系电话">
          <a-input v-model="highSearchForm.litigantPhone" placeholder="请输入内容" allow-clear />
        </a-form-item>
        <a-form-item field="litigantType" label="当事人类型">
          <a-select
            v-model="highSearchForm.litigantTypes"
            :options="litigantTypeOptions"
            placeholder="请选择"
            allow-clear
            multiple
            :max-tag-count="1"
          />
        </a-form-item>
        <a-form-item field="litigantIdentityType" label="当事人身份">
					<DictDataSelect v-model="highSearchForm.litigantIdentityTypes" :dict-type="DictTypeEnum.litigant_identity_type" :multiple="true" />
        </a-form-item>
        <a-form-item field="litigantPhoneStatus" label="号码状态">
          <a-select
            v-model="highSearchForm.litigantPhoneStatuses"
            :options="litigantPhoneStatusOptions"
            :max-tag-count="1"
            placeholder="请选择"
            allow-clear
            multiple
          />
        </a-form-item>

        <!-- 任务实例查询组件 -->
        <TaskInstanceSearch v-model="highSearchForm.taskInstance" />

      </a-form>
      <template #footer>
        <div class="flex justify-center">
          <a-button @click="handleHighSearchReset">重置</a-button>
          <a-button class="ml20" type="primary" @click="handleHighSearchQuery">查询</a-button>
        </div>
      </template>
    </a-drawer>
    <a-modal
      width="570px"
      title="案件导入"
      :visible="importVisible"
      ok-text="导入"
      @ok="handleImportConfirm"
      @cancel="handleImportCancel"
    >
      <a-form
        ref="importFormRef"
        :model="importForm"
        :label-col-props="{ span: 9 }"
        :wrapper-col-props="{ span: 15 }"
        label-align="left"
      >
        <div class="form-title">案件信息</div>
				<a-form-item field="businessType" label="业务类型" :rules="[{ required: true, message: '请选择业务类型' }]">
					<BusinessTypeSelect v-model="importForm.businessType" @change="handleBusinessTypeChange" />
				</a-form-item>
        <a-form-item field="entrustsId" label="案源方" :rules="[{ required: true, message: '请选择案源方' }]">
          <a-select
            v-model="importForm.entrustsId"
            :options="entrustsOptions"
            placeholder="请选择"
            allow-clear
            @change="handleEntrustsChange"
          />
        </a-form-item>
        <a-form-item field="entrustsId" label="调解类型" :rules="[{ required: true, message: '请选择调解类型' }]">
          <a-select
            v-model="importForm.caseMediateType"
            :options="dict.caseMediateTypeOptions"
            placeholder="请选择"
            allow-clear
          />
        </a-form-item>
        <a-form-item
          v-if="importForm.entrustsId"
          field="entrustsDeptId"
          label="所属部门"
          :rules="[{ required: true, message: '请选择所属部门' }]"
        >
          <a-select
            v-model="importForm.entrustsDeptId"
            :options="entrustsDeptOptions"
            placeholder="请选择"
            allow-clear
          />
        </a-form-item>
        <a-form-item field="dateConfigType" label="调解时限设置方式" :rules="[{ required: true, message: '请选择调解时限设置方式' }]">
          <a-radio-group v-model="importForm.dateConfigType">
            <a-radio v-for="item in dateConfigTypeOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="importForm.dateConfigType === '2'" field="allTime" label="调解时限">
          <a-range-picker v-model="importForm.allTime" style="width: 100%" @change="handlePickerSelect">
            <template #suffix-icon><icon-schedule /></template>
          </a-range-picker>
        </a-form-item>
        <a-form-item field="completeReview" label="办结审核" :rules="[{ required: true, message: '请选择是否办结审核' }]">
          <a-switch
            v-model="importForm.completeReview"
            :checked-value="1"
            :unchecked-value="0"
            :default-checked="importForm.completeReview === 1"
          ></a-switch>
        </a-form-item>

				<a-form-item field="isDesensitize" label="是否脱敏" :rules="[{ required: true, message: '请选择是否脱敏' }]">
					<a-switch v-model="importForm.isDesensitize" />
				</a-form-item>

        <template v-if="importForm.entrustsId">
          <div class="form-title">模板数据</div>
          <a-form-item field="deputyTmplId" label="导入模板" :rules="[{ required: true, message: '请选择导入模板' }]">
            <a-select v-model="importForm.deputyTmplId" :options="tmplOptions" placeholder="请选择" allow-clear />
            <a-button v-if="importForm.deputyTmplId" class="ml10" type="primary" @click="handleImportModalDownload">
              下载
            </a-button>
          </a-form-item>
          <a-form-item field="fileList" label="上传模板数据" :rules="[{ required: true, message: '请上传模板数据' }]">
            <a-upload
              v-model:file-list="importForm.fileList"
              draggable
              :auto-upload="false"
              :limit="1"
              accept=".xls, .xlsx"
              @change="handleImportFileChange"
            >
              <template #upload-button>
                <a-button><icon-upload /><span class="ml4">点击或拖拽至此上传文件</span></a-button>
              </template>
            </a-upload>
          </a-form-item>
<!--          <a-form-item field="isIgnoreError" label="仅导入有效数据（忽略错误）">-->
<!--            <a-switch v-model="importForm.isIgnoreError"></a-switch>-->
<!--          </a-form-item>-->
        </template>
      </a-form>
    </a-modal>
    <a-modal
      width="500px"
      title="案件导出"
      :visible="exportVisible"
      ok-text="导出"
      @ok="handleExportConfirm"
      @cancel="handleExportCancel"
    >
      <div>
        <div class="form-title">案件信息</div>
        <div class="ml20 mb16">
          已选择案件数量：
          <span class="ml4 mr4 bold">
            {{ ifChooseAll ? '全部案件' : `${selectedKeys.length} 件` }}
          </span>
        </div>
        <div class="form-title">请选择导出信息</div>
        <a-table
          v-model:selected-keys="selectedExportTemlKeys"
          row-key="deputyTmplId"
          :pagination="false"
          :columns="exportColumns"
          :data="exportTmplList"
          :row-selection="rowSelection"
          :scroll="{ y: '160px' }"
        >
        </a-table>
      </div>
    </a-modal>
    <a-modal
      width="500px"
      title="案件更新"
      :visible="updateVisible"
      ok-text="更新"
      @ok="handleUpdateConfirm"
      @cancel="handleUpdateCancel"
    >
      <a-form
        ref="updateFormRef"
        :model="updateForm"
        :label-col-props="{ span: 8 }"
        :wrapper-col-props="{ span: 16 }"
        label-align="left"
      >
        <div class="form-title">案件信息</div>
        <a-form-item field="entrustsId" label="案源方" :rules="[{ required: true, message: '请选择案源方' }]">
          <a-select
            v-model="updateForm.entrustsId"
            :options="entrustsOptions"
            placeholder="请选择"
            allow-clear
            @change="handleEntrustsChangeForUpdate"
          />
        </a-form-item>
        <a-form-item
          v-if="updateForm.entrustsId"
          field="entrustsDeptId"
          label="所属部门"
          :rules="[{ required: true, message: '请选择所属部门' }]"
        >
          <a-select
            v-model="updateForm.entrustsDeptId"
            :options="entrustsDeptOptions"
            placeholder="请选择"
            allow-clear
          />
        </a-form-item>

        <template v-if="updateForm.entrustsId">
          <div class="form-title">模板数据</div>
          <a-form-item field="deputyTmplId" label="更新模板" :rules="[{ required: true, message: '请选择更新模板' }]">
            <a-select v-model="updateForm.deputyTmplId" :options="updateTmplOptions" placeholder="请选择" allow-clear />
            <a-button v-if="updateForm.deputyTmplId" class="ml10" type="primary" @click="handleUpdateModalDownload">
              下载
            </a-button>
          </a-form-item>
          <a-form-item field="fileList" label="上传模板数据" :rules="[{ required: true, message: '请上传模板数据' }]">
            <a-upload
              v-model:file-list="updateForm.fileList"
              draggable
              :auto-upload="false"
              :limit="1"
              accept=".xls, .xlsx"
              @change="handleUpdateFileChange"
            >
              <template #upload-button>
                <a-button><icon-upload /><span class="ml4">点击或拖拽至此上传文件</span></a-button>
              </template>
            </a-upload>
          </a-form-item>
          <a-form-item field="isIgnoreError" label="仅导入有效数据（忽略错误）">
            <a-switch v-model="updateForm.isIgnoreError"></a-switch>
          </a-form-item>
        </template>
      </a-form>
    </a-modal>
    <a-modal
      :visible="assignVisible"
      width="500px"
      title="案件分派"
      ok-text="分派"
      @ok="handleAssignConfirm"
      @cancel="handleAssignCancel"
    >
      <a-form
        ref="assignFormRef"
        :model="assignForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
      >
        <a-form-item label="已选案件数：">
          <span class="bold mr4">
            {{ ifChooseAll ? '全部案件' : `${selectedKeys.length} 件` }}
          </span>
        </a-form-item>
        <a-form-item field="orgType" label="分派至：" :rules="[{ required: true, message: '请选择分派至' }]">
          <a-radio-group v-model="assignForm.orgType">
            <a-radio v-rule="needRule" value="3">调解组织</a-radio>
            <a-radio v-rule="['调解组织管理员']" value="2">调解团队</a-radio>
            <a-radio v-rule="['调解组织管理员', '调解组长']" value="1">调解员</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item field="orgId" label="调解组织：" :rules="[{ required: true, message: '请选择调解组织' }]">
          <a-select
            v-model="assignForm.orgId"
            :options="orgNameOptions"
            placeholder="请选择"
            allow-clear
            @change="handleMediationOrganizationChangeForAssign"
          />
        </a-form-item>
        <a-form-item
          v-if="assignForm.orgType === '2' || assignForm.orgType === '1'"
          field="deptId"
          label="调解团队："
          :rules="[{ required: true, message: '请选择调解团队' }]"
        >
          <a-select
            v-model="assignForm.deptId"
            :options="deptNameOptions"
            placeholder="请选择"
            allow-clear
            :disabled="!assignForm.orgId"
            @change="handleMediationTeamChangeForAssign"
          />
        </a-form-item>
        <a-form-item
          v-if="assignForm.orgType === '1'"
          field="currentMediatorId"
          label="调解员："
          :rules="[{ required: true, message: '请选择调解员' }]"
        >
          <a-select
            v-model="assignForm.currentMediatorId"
            :options="currentMediatorNameOptions"
            placeholder="请选择"
            allow-clear
            :disabled="!assignForm.deptId"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <NumberCheckModal
      ref="numberCheckModalRef"
      v-model:visible="numberCheckVisible"
      :case-ids="selectedKeys"
      :if-choose-all="ifChooseAll"
      title="号码检测"
      @comfirm="numberCheckVisible = false"
      @cancel="numberCheckVisible = false"
    />
    <CallRepairModal
      ref="callRepairModalRef"
      v-model:visible="callRepairVisible"
      :case-ids="selectedKeys"
      :if-choose-all="ifChooseAll"
      :call-state="callState"
      title="失联修复"
      @comfirm="callRepairVisible = false"
      @cancel="callRepairVisible = false"
    />
    <SmsSendModal
      ref="smsSendModalRef"
      v-model:visible="smsSendVisible"
      :case-ids="selectedKeys"
      :if-choose-all="ifChooseAll"
      title="短信发送"
      @comfirm="smsSendVisible = false"
      @cancel="smsSendVisible = false"
    />
    <!-- 批量生成文书 -->
    <DocGenerateModal
      ref="docGenerateModalRef"
      v-model:visible="docGenerateVisible"
      :case-ids="selectedKeys"
      :if-choose-all="ifChooseAll"
      title="批量生成文书"
      @comfirm="docGenerateVisible = false"
      @cancel="docGenerateVisible = false"
    />

    <!-- 批量任务登记-->
    <TaskInstanceModal
      v-model:visible="batchTaskRegisterVisible"
      :case-id="''"
      :case-id-list="selectedKeys"
      :if-choose-all="ifChooseAll"
      :mediate-case-page-b-o="allSearchForm"
      mode="create"
      @success="handleBatchTaskRegisterSuccess"
      @cancel="batchTaskRegisterVisible = false"
    />

    <!-- 批量办结申请-->
    <StartApprovalModal
      v-model:visible="batchApprovalVisible"
      :width="600"
      :case-id-list="selectedKeys"
      :if-choose-all="ifChooseAll"
      title="批量申请办理"
    />
  </div>
</template>

<script lang="ts" setup>
import { caseDistribute, findDistributeCase } from '@/api/arcoApi/mediateManage/jobLog/caseAllocationRecord'
import {
	caseBatchImport,
	exportCase,
	getDept,
	getExportTmpl,
	getMdtMediator,
	getOrg,
	pageList
} from '@/api/arcoApi/caseManage/caseManage'
import { getDeptInfoById } from '@/api/arcoApi/businessConf/caseSourceInfoManage'
import { findAllEntrusts, findEntrustsOrg } from '@/api/eleApi/case/batchManage'
import { deputyTmplList, downloadTmpl } from '@/api/arcoApi/businessConf/caseTemplateManage'
// import { getAllCaseId } from '@/api/arcoApi/caseManage/caseManage'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { CascaderOption } from '@arco-design/web-vue/es/cascader/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import { Message, Modal, type RangePickerInstance } from '@arco-design/web-vue'
import type { TreeNodeData } from '@arco-design/web-vue/es/tree/interface'
import type { UploadInstance } from '@arco-design/web-vue/es/upload'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import StartApprovalModal from '@/views/case/caseManage/components/start-approval-modal.vue'
import CustomModuleSearch from './components/custom-module-search.vue'
import DeptUserSelect from '@/components/deptUserSelect/index.vue'
import DocGenerateModal from './components/doc-generate-modal.vue'
import NumberCheckModal from './components/number-check-modal.vue'
import DictDataSelect from '@/components/dictDataSelect/index.vue'
import CallRepairModal from './components/call-repair-modal.vue'
import SmsSendModal from './components/sms-send-modal.vue'
import TaskInstanceModal from '@/views/case/caseDetails/components/modal/task-instance-modal.vue'
import TaskInstanceSearch from './components/task-instance-search.vue'

import { computed, nextTick, onMounted, onUnmounted, provide, reactive, ref } from 'vue'
import { useDictStore, useTabBarStore, useUserStore } from '@arco/store'
import { timeToDateStr } from '@/utils/dateUtil'
import { useRoute, useRouter } from 'vue-router'
import { dictEnumValToObject, removeNullParam } from '@/utils'
import sysUtil from '@/utils/sysUtil'
import dict from '@/dict/caseManage'
import _ from 'lodash'
import { VxeGridInstance, VxeGridListeners, VxeGridProps, VxeGridPropTypes, VxeTableDefines } from 'vxe-table'
import { BusinessTypeEnum, DictTypeEnum } from '@/dict/systemManage.ts'
import BusinessTypeSelect from '@/components/businessType/businessTypeSelect.vue'


const tabBarStore = useTabBarStore()
const userStore = useUserStore()
const dictStore = useDictStore()
const router = useRouter()
const route = useRoute()
const gridRef = ref<VxeGridInstance>();

const currentTagRoute = tabBarStore.tagList.find((tagRoute) => tagRoute.name === route.name)
const routeParams = currentTagRoute?.params
// const routeQuery = route.query

const CustomModuleSearchRef = ref<InstanceType<typeof CustomModuleSearch>>()
const customModuleModalVisible = ref<boolean>(false)
const searchFormModel = () => {
  return {
    cascaderEntrustsIds: [], // 仅作为数据回显，不参与列表数据筛选，防止浏览器刷新后筛选缓存不展示
    entrustsOrgBOList: [],
    mdtCaseStatuses: [],
    mediateStatuses: [],
    createStartTime: '',
    createEndTime: '',
    mediateResult: '',
    entrustsNames: [],
    createTime: [],
    caseNo: ''
  }
}

const highSearchFormModel = () => {
  return {
    litigantIdentityTypes: [],
    litigantPhoneStatuses: [],
    expirationTimeStart: null,
    mediateCloseTimeStart: '',
    mediateCloseTimeEnd: '',
    caseExpirationBegin: '',
    expirationTimeEnd: null,
    caseNatureIds: [],
    currentMediatorIds: [],
    caseExpirationEnd: '',
    mediateCloseTime: [],
    closeStartTime: '',
    caseApplyBegin: '',
    caseStartBegin: '',
    caseStartEnd: '',
    successReasons: [],
    caseExpiraTime: [],
    litigantTypes: [],
    caseStartTime: [],
    litigantPhone: '',
    litigantName: '',
    caseApplyEnd: '',
    closeReasons: [],
    closeEndTime: '',
    closeReason: [],
    applyTime: [],
    closeTime: [],
    deptIds: [],
    orgIds: [],
    businessTypes: [],
    caseMediateTypes: [],
    // 新的任务实例查询对象
    taskInstance: {
      assignAccountIdList: [],
      taskTypes: [],
      assignTimeStart: undefined,
      assignTimeEnd: undefined,
      deadlineStart: undefined,
      deadlineEnd: undefined,
      managerIdList: [],
      taskStatuses: [],
      finishAccountIdList: [],
      finishTimeStart: undefined,
      finishTimeEnd: undefined,
      remark: undefined,
      assignTime: [],
      deadline: [],
      finishTime: []
    }
  }
}

const highSearchForm = ref<CASE_HIGH_SEARCH_FORM_TYPE>(highSearchFormModel())
const searchForm = ref<CASE_SEARCH_FORM_TYPE>(searchFormModel())
const renderData = ref<REQUEST_GET_CASE_LIST_TYPE[]>([])
const highSearchFormRef = ref<FormInstance>()
const importFormRef = ref<FormInstance>()
const updateFormRef = ref<FormInstance>()
const assignFormRef = ref<FormInstance>()

// auth
const needRule = computed(() => userStore.userInfo.companyType === '3')

const pagination = reactive< VxeGridPropTypes.PagerConfig>({
	pageSizes: [10, 20, 30, 50, 100, 200, 500],
	pageSize: 10,
	currentPage: 1,
	total: 0,
	layouts: ['Total','Home', 'PrevPage', 'Number', 'NextPage', 'End', 'Sizes', 'FullJump']
})


let sortCheckedParams:VxeTableDefines.SortCheckedParams[]= [];

const rowSelection: TableRowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false
})

const selectedKeys = ref<Array<any>>([])

const gridOptions = reactive<VxeGridProps<REQUEST_GET_CASE_LIST_TYPE>>({
	id: 'caseSearchTableList',
	border: true,
	stripe: true,
	showOverflow: true,
	minHeight:500,
	sortConfig: {
		remote: true
	},
	customConfig: {
		storage: true
	},
	columnConfig: {
		resizable: true,
		drag: true
	},
	columnDragConfig: {
		trigger: 'cell',
		showIcon: false,
		showGuidesStatus: true
	},
	rowConfig: {
		isCurrent: true,
		isHover: true
	},
	currentRowConfig:{
		isFollowSelected:true
	},
	resizableConfig: {
		isDblclickAutoWidth: true
	},
	toolbarConfig: {
		custom: true,
		zoom: true
	},
	pagerConfig: pagination,
	columns:  [
		{ field: 'caseId', type: 'checkbox', fixed: 'left', width: 80 },
		{ field: 'caseNo', width: 300,fixed: 'left', title: '案件编号', showOverflow: true , slots: { default: 'caseNo' }},
		{ field: 'defendAntLitigantNames', title: '申请人', width: 140, showOverflow: true },
    { field: 'plainTiffLitigantNames', title: '被申请人', width: 140, showOverflow: true },
    { field: 'caseSubjectMatter', title: '标的额',width: 120, showOverflow: true},
    { field: 'otherDemand', title: '最新调解记录', width: 180, showOverflow: true },
    { field: 'nextDoTime', title: '下次跟进时间', width: 150, showOverflow: true, slots: { default: 'nextDoTime' } },
		{ field: 'caseNatureContent', title: '案由', width: 180, showOverflow: true },
		{ field: 'entrustsName', title: '案源方', width: 150, showOverflow: true },
		{ field: 'entrustsDeptName', title: '部门', width: 140, showOverflow: true },
		{ field: 'mdtCaseStatus', title: '案件状态', width: 140, slots: { default: 'mdtCaseStatus' } },
		{ field: 'mediateStatus', title: '调解状态', width: 140, align: 'center', slots: { default: 'mediateStatus' } },
		{ field: 'mediateResult', title: '调解结果', width: 170, align: 'center', slots: { default: 'mediateResult' } },
		{ field: 'caseManageCloseReason', title: '案管办结结果', width: 130, align: 'center', slots: { default: 'caseManageCloseReason' } },
		{ field: 'orgName', title: '调解组织', width: 150, showOverflow: true },
		{ field: 'deptName', title: '调解团队', width: 150, showOverflow: true },
		{ field: 'businessTypeName', title: '业务类型', width: 150, showOverflow: true },
		{ field: 'currentMediatorName', title: '调解员', width: 140, showOverflow: true },
		{ field: 'caseApplyTime', title: '案件申请时间', width: 180, showOverflow: true, slots: { default: 'caseApplyTime' } },
		{ field: 'mediateBeginTime', title: '调解开始时间', width: 180, showOverflow: true, slots: { default: 'mediateBeginTime' } },
		{ field: 'expirationTime', title: '调解截止时间', width: 180, showOverflow: true, slots: { default: 'expirationTime' }, sortable: true },
		{ field: 'closeTime', title: '调解办结时间', width: 180, showOverflow: true, slots: { default: 'closeTime' } },
		{ field: 'caseManageCloseTime', title: '案管办结时间', width: 180, showOverflow: true },
		{ field: 'mediateDoneTime', title: '调解完成时间', width: 180, showOverflow: true },
		{ field: 'mediateCloseTime', title: '案件关闭时间', width: 180, showOverflow: true },
		{ field: 'remainingDays', title: '剩余调解时效', width: 120, align: 'center', slots: { default: 'remainingDays' } },
		{ field: 'createTime', title: '创建时间', width: 180, showOverflow: true },
		{ field: 'operations', title: '操作', width: 80, align: 'center', fixed: 'right', slots: { default: 'operations' } }
	],
	mouseConfig: {
		selected: true
	},
	keyboardConfig: {
		isArrow: true
	}
});

const litigantPhoneStatusOptions = ref<SelectOptionData[]>(dict.litigantPhoneStatusOptions)
const dateConfigTypeOptions = ref<SelectOptionData[]>(dict.dateConfigTypeOptions)
const mdtCaseStatusOptions = ref<SelectOptionData[]>(dict.mdtCaseStatusOptions)
const mediateResultOptions = ref<SelectOptionData[]>(dict.mediateResultOptions)
const litigantTypeOptions = ref<SelectOptionData[]>(dict.litigantTypeOptions)
const businessTypeOptions = ref<SelectOptionData[]>(dict.businessType)
const currentMediatorNameOptions = ref<SelectOptionData[]>([])
const cascaderEntrustOptions = ref<CascaderOption[]>([])
const entrustsOptions = ref<SelectOptionData[]>([])
const deptNameOptions = ref<SelectOptionData[]>([])
const orgNameOptions = ref<SelectOptionData[]>([])

const ifChooseAll = ref(false)
const visible = ref(false)

const businessTypeObj = computed(() => {
  return dictEnumValToObject(dict.businessType)
})

const transTreeSelectOptions = (arr) => {
  let arrMap = arr.map((e) => {
    return {
      children: e.children ? transTreeSelectOptions(e.children) : [],
      title: e.label,
      value: e.value,
      key: e.value
    }
  })
  return arrMap
}

function optionsGroupConvertTree(optionsGroup) {
  let result: TreeNodeData[] = []
  for (const businessType in optionsGroup) {
    result.push({
      children: transTreeSelectOptions(optionsGroup[businessType]),
      title: businessTypeObj.value[businessType],
      selectable: false,
      checkable: false
    })
  }
  return result
}

const successReasonOptionsGroupTree = optionsGroupConvertTree(dict.successReasonOptionsGroup)

const mdtCaseStatusObj = computed(() => {
  return dictEnumValToObject(dict.mdtCaseStatusOptions)
})

const mediateResultObj = computed(() => {
  return dictEnumValToObject(dict.mediateResultOptions)
})

const caseMediateTypeObj = computed(() => {
  return dictEnumValToObject(dict.caseMediateTypeOptions)
})

const closeReasonsObj = computed(() => {
  return dictEnumValToObject(dict.closeReasonsCascaderOptions)
})

const successReasonsObj = computed(() => {
  return dictEnumValToObject(dict.successReasonOptions)
})

const allSearchForm = computed(() => {
  return removeNullParam(
    {
      ...CustomModuleSearchRef.value?.getSearchCustomForm(),
      ...highSearchForm.value,
      ...searchForm.value
    },
    'cascaderEntrustsIds'
  )
})

// const indeterminate = computed(() => {
//   if (selectedKeys.value.length === 0) return false
//   return  selectedKeys.value.length < pagination.total
// })

const handleRouteParamsFormData = (formData: OBJ_KEY_STR_ANY_TYPE) => {
  for (const key in formData) {
    if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
      formData[key] = routeParams[key]
    }
  }
}

if (routeParams && Object.keys(routeParams).length > 0) {
  handleRouteParamsFormData(highSearchForm.value)
  handleRouteParamsFormData(searchForm.value)
  if (routeParams.pagination) {
    pagination.pageSize = routeParams.pagination.pageSize || 10
    pagination.currentPage = routeParams.pagination.currentPage || 1
  }
	if (routeParams.orderParams){
		sortCheckedParams = routeParams.sortCheckedParams;
		gridRef.value?.setSort(sortCheckedParams)
	}
}

provide('mediateCasePageBO', allSearchForm)

const handleAllSelect = async (checkboxVal: boolean) => {
	if (checkboxVal) {
		selectedKeys.value = renderData.value.map(item => item.caseId);
		gridRef.value?.setAllCheckboxRow(true); // 同步 vxe-grid 全选状态
	} else {
		selectedKeys.value = [];
		gridRef.value?.clearCheckboxRow(); // 同步 vxe-grid 取消全选状态
	}
  ifChooseAll.value = checkboxVal;
};

const gridEvents: VxeGridListeners = {
	sortChange() {
		selectedKeys.value = []
		if(gridRef.value?.getSortColumns()){
			sortCheckedParams = gridRef.value?.getSortColumns()
		}
		pagination.currentPage = 1;
		getTableData();
	},
	pageChange ({ pageSize, currentPage }) {
		selectedKeys.value = []
		pagination.currentPage = currentPage;
		pagination.pageSize = pageSize;
		getTableData();

		//换页就取消全选
		ifChooseAll.value = false;

		if (gridRef.value) {
			console.log(gridRef.value.getSortColumns())
		}
  },
	checkboxAll({ checked }){
		const gridInstance = gridRef.value;
		if (gridInstance) {
			const selectedRows = gridInstance.getCheckboxRecords();
			selectedKeys.value = selectedRows.map(item => item.caseId);
			console.log('selectedKeysAll.value', selectedKeys.value)
		}
		if(!checked){
			ifChooseAll.value = false;
		}
	},
  checkboxChange() {
        const gridInstance = gridRef.value;
        if (gridInstance) {
            const selectedRows = gridInstance.getCheckboxRecords();
            selectedKeys.value = selectedRows.map(item => item.caseId);

						console.log('selectedKeys.value', selectedKeys.value)
					//如果选中的内容少于当前页则取消全选
					if (!gridInstance.isAllCheckboxChecked()) {
						ifChooseAll.value = false
					}
        }
    }
};
const getTableData = async () => {
	try {
		gridOptions.loading = true

		let orderParams = [];
		if(sortCheckedParams){
			orderParams = sortCheckedParams.map(item => ({ field: item.field, orderKeyWord: item.order }));
		}

		let searchParams = removeNullParam(allSearchForm.value)

		const data: any = await pageList({
			pageInfo: { size: pagination.pageSize, pageNumber: pagination.currentPage,orderParams:orderParams },
			param: searchParams
		})

		pagination.total = data.total
		renderData.value = data.list

		// 奇怪的需求，案件列表每次点击查询新开一个标签页，保持上次查询
		// 20241010需求调整，案件列表保持唯一，每次查询，保持查询记录和分页记录
		tabBarStore.updateTagParams('caseManage', { pagination: pagination, ...searchParams ,...sortCheckedParams})
	} catch (err) {
		console.error(err)
	} finally {
		gridOptions.loading = false
	}
}


const search = () => {
  pagination.currentPage = 1
  selectedKeys.value = []
  getTableData()
}

const reset = () => {
  searchForm.value = searchFormModel()
  CustomModuleSearchRef.value?.resetForm()
  getTableData()
}

const getAllEntrusts = () => {
  findAllEntrusts().then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

const getCascaderEntrusts = () => {
  findEntrustsOrg().then((res: any) => {
    if (res && res.length) {
      cascaderEntrustOptions.value = res.map((entrustItem) => {
        let entrustRow = {
          value: `${entrustItem.type}-${entrustItem.entrustsId}`,
          label: entrustItem.entrustsName,
          children: []
        }
        if (entrustItem.entrustsDeptDTOList) {
          entrustRow.children = entrustItem.entrustsDeptDTOList.map((deptItem) => {
            let depttRow = {
              value: `${deptItem.type}-${deptItem.deptId}`,
              label: deptItem.deptName,
              children: []
            }
            if (deptItem.entrustsJudgeDTOList) {
              depttRow.children = deptItem.entrustsJudgeDTOList.map((judgeItem) => ({
                value: `${judgeItem.type}-${judgeItem.judgeId}`,
                label: judgeItem.judgeName
              }))
            }
            return depttRow
          })
        }
        return entrustRow
      })
      if (searchForm.value.cascaderEntrustsIds) {
        handleCascaderEntrusts(searchForm.value.cascaderEntrustsIds)
      }
    }
  })
}

getCascaderEntrusts()
getAllEntrusts()
//等其他组件加载完再刷新列表
onMounted(() => {
  nextTick(getTableData)
  window.addEventListener('keydown', (e) => {
    if (e.code === 'Enter' || e.keyCode === 13) {
      getTableData()
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('keydown', () => {})
})

// 案件高级查询
const getMediationStaffList = async (deptIds: (string | number)[]) => {
  if (!deptIds.length && assignVisible.value) {
    return false
  }
  let mediatorRes = await getMdtMediator({ deptIds: deptIds,orgIds: highSearchForm.value.orgIds})
	if (mediatorRes?.length) {
    currentMediatorNameOptions.value = mediatorRes.map((item) => ({
      label: item.employeeName,
      value: item.accountId
    }))
  }
}

const handleApplyTimeChange = (times: any) => {
  if (times && times.length) {
    highSearchForm.value.caseApplyBegin = times[0] || ''
    highSearchForm.value.caseApplyEnd = times[1] || ''
  } else {
    highSearchForm.value.caseApplyBegin = ''
    highSearchForm.value.caseApplyEnd = ''
  }
}

const handleCascaderEntrusts = (value: any) => {
  if (value && value.length) {
    searchForm.value.entrustsOrgBOList = value.map((key) => {
      let keys: string[] = key.split('-')
      return { type: keys[0], orgId: keys[1] }
    })
  } else {
    searchForm.value.entrustsOrgBOList = []
  }
}

const handleCaseStartTimeChange = (times: any) => {
  if (times && times.length) {
    highSearchForm.value.caseStartBegin = times[0] || ''
    highSearchForm.value.caseStartEnd = times[1] || ''
  } else {
    highSearchForm.value.caseStartBegin = ''
    highSearchForm.value.caseStartEnd = ''
  }
}

const handleCaseExpiraTimeChange = (times: any) => {
  if (times && times.length) {
    highSearchForm.value.caseExpirationBegin = times[0] || ''
    highSearchForm.value.caseExpirationEnd = times[1] || ''
  } else {
    highSearchForm.value.caseExpirationBegin = ''
    highSearchForm.value.caseExpirationEnd = ''
  }
}

const handleCaseCloseTimeChange: RangePickerInstance['onChange'] = (times) => {
  if (times && times.length) {
    highSearchForm.value.mediateCloseTimeStart = (times[0] as string) || ''
    highSearchForm.value.mediateCloseTimeEnd = (times[1] as string) || ''
  } else {
    highSearchForm.value.mediateCloseTimeStart = ''
    highSearchForm.value.mediateCloseTimeEnd = ''
  }
}

const getMediationTeamList = async (orgIds: (string | number)[]) => {
  if (!orgIds.length) {
    return false
  }
  let deptRes = await getDept(orgIds)
  if (deptRes?.length) {
    deptNameOptions.value = deptRes.map((item) => ({
      label: item.deptName,
      value: item.deptId
    }))
  }
}

const getMediationOrganizationList = async () => {
  let orgRes = await getOrg()
  if (orgRes?.length) {
    orgNameOptions.value = orgRes.map((item) => ({
      label: item.orgName,
      value: item.orgId
    }))
  }
}

const handleHighSearch = async () => {
  let { orgIds, deptIds } = highSearchForm.value
  await getMediationOrganizationList()
  await getMediationTeamList(orgIds)
  await getMediationStaffList(deptIds)
  visible.value = true
}

const handleHighSearchReset = () => {
  highSearchForm.value = highSearchFormModel()
  getTableData()
}

const handleHighSearchQuery = () => {
  visible.value = false
  search()
}

const handleMediationOrganizationChange = () => {
  highSearchFormRef.value?.resetFields(['deptIds', 'currentMediatorIds'])
  currentMediatorNameOptions.value = []
  deptNameOptions.value = []
	highSearchForm.value.currentMediatorIds = []
  getMediationTeamList(highSearchForm.value.orgIds)
	getMediationStaffList(highSearchForm.value.deptIds)
}

const handleMediationTeamChange = () => {
  highSearchFormRef.value?.resetFields(['currentMediatorIds'])
  currentMediatorNameOptions.value = []
	highSearchForm.value.currentMediatorIds = []
  getMediationStaffList(highSearchForm.value.deptIds)
}

const createImpFormModel = () => {
  return {
    mediateBeginTime: '',
    isIgnoreError: false,
    dateConfigType: '1',
    entrustsDeptId: '',
    mediateEndTime: '',
    completeReview: 1,
    deputyTmplId: '',
    entrustsId: '',
    fileList: [],
    impFlag: '',
    allTime: [],
    caseMediateType: '',
		businessType: null,
		isDesensitize:true
  }
}

// 案件导入
const importForm = ref<CASE_IMPORT_FORM_TYPE>(createImpFormModel())
const entrustsDeptOptions = ref<SelectOptionData[]>([])
const tmplOptions = ref<SelectOptionData[]>([])
const importVisible = ref<boolean>(false)
const getDeputyTmplList = () => {
  tmplOptions.value = []
  let params = {
    tmplStatus: 1,
    tmplType: '2', // 案件导入模板
    entrustsId: importForm.value?.entrustsId
  }
  deputyTmplList(params).then((res) => {
    if (res && res.length) {
      tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.deputyTmplId }))
    }
  })
}
const getEntrustsDeptList = (id: string | number) => {
  getDeptInfoById(id).then((res) => {
    if (res && res.length) {
      entrustsDeptOptions.value = res.map((item) => ({ label: item.deptName, value: item.deptId }))
    }
  })
}
// 处理业务类型变化，自动设置脱敏状态
const handleBusinessTypeChange = (value: string | number, ancestorPath: (string | number)[]) => {
  if (value && ancestorPath) {
    // 判断祖先路径中是否包含政务类型（value=1）
    const isGovType = ancestorPath.includes(BusinessTypeEnum.government);
    // 根据判断结果设置脱敏状态
    importForm.value.isDesensitize = isGovType;
  }
}

const handleEntrustsChange = () => {
  importFormRef.value?.resetFields(['deputyTmplId', 'entrustsDeptId'])
  importForm.value.fileList = []
  if (importForm.value.entrustsId) {
    getDeputyTmplList()
    getEntrustsDeptList(importForm.value.entrustsId)
  }
}
const handleCaseCreate = () => {
  router.push({ name: `caseCreate` })
}
const handleCaseImport = () => {
  importVisible.value = true
}
const handleImportModalDownload = async () => {
  let params: any = {
    tmplId: importForm.value.deputyTmplId,
    tmplType: '2' // 案件导入模板
  }
  try {
    let res = await downloadTmpl(params)
    Message.success('下载成功')
    sysUtil.blobExport(res)
  } catch (error) {
    Message.error('下载失败')
  }
}
const openImportReocrdDialog = (massage) => {
  Modal.success({
    title: '操作成功，请至案件导入记录查看校验过程及结果',
    content: massage,
    onOk: () => {
      handleImportCancel()
      router.push({ name: 'caseImportRecord' })
    }
  })
}

const handleImportConfirm = async () => {
  const res = await importFormRef.value?.validate()
  if (!res) {
    let list: any = []
    importForm.value.fileList.map((item: any) => list.push(item.file))
    let param = new FormData()
    param.append('dateConfigType', importForm.value.dateConfigType)
    param.append('deputyTmplId', importForm.value.deputyTmplId)
    param.append('entrustsId', importForm.value.entrustsId)
    param.append('entrustsDeptId', importForm.value.entrustsDeptId)
    param.append('completeReview', String(importForm.value.completeReview))
    param.append('isIgnoreError', String(importForm.value.isIgnoreError))
    param.append('caseMediateType', String(importForm.value.caseMediateType))
    param.append('businessType', String(importForm.value.businessType))
    param.append('isDesensitize', importForm.value.isDesensitize)
    if (importForm.value.dateConfigType === '2') {
      param.append('mediateBeginTime', importForm.value.mediateBeginTime)
      param.append('mediateEndTime', importForm.value.mediateEndTime)
    }
    param.append('impFlag', '1')
    if (list.length) {
      param.append('file', list[0])
    }
    const result: any = await caseBatchImport(param)
    if (result.success === false) {
      if (result.massage) {
        result.massage = result.massage.replaceAll('\n', '\r\n')
      }
      Modal.error({ title: '案件导入失败', content: result.massage, bodyStyle: 'white-space:pre-wrap' })
    } else {
      openImportReocrdDialog(result.massage)
    }
  }
  return false
}

const handleImportCancel = () => {
  importForm.value = createImpFormModel()
  importFormRef.value?.clearValidate()
  importVisible.value = false
  search()
}

const handleImportFileChange: UploadInstance['onChange'] = (_, item) => {
  let type = item.name?.split('.')[1]
  if (type !== 'xls' && type !== 'xlsx') {
    Message.warning('请选择xls或者xlsx文件')
    importForm.value.fileList = []
  }
}
// 案件更新
const updateForm = ref<CASE_IMPORT_FORM_TYPE>(createImpFormModel())
const updateTmplOptions = ref<SelectOptionData[]>([])
const updateVisible = ref<boolean>(false)
const getDeputyTmplListForUpdate = () => {
  updateTmplOptions.value = []
  let params = {
    tmplStatus: 1,
    tmplType: '3', // 案件更新模板
    entrustsId: updateForm.value?.entrustsId
  }
  deputyTmplList(params).then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      updateTmplOptions.value = data.map((item) => ({ label: item.tmplTitle, value: item.deputyTmplId }))
    }
  })
}

const handlePickerSelect = (times: any) => {
  if (times && times.length) {
    importForm.value.mediateBeginTime = times[0] || ''
    importForm.value.mediateEndTime = times[1] || ''
  } else {
    importForm.value.mediateBeginTime = ''
    importForm.value.mediateEndTime = ''
  }
}

const handleCreateTimeSelect = (times: any) => {
  searchForm.value.createStartTime = times[0] || ''
  searchForm.value.createEndTime = times[1] || ''
}

const handleCloseTimeSelect = (times: any) => {
  highSearchForm.value.closeStartTime = times[0] || ''
  highSearchForm.value.closeEndTime = times[1] || ''
}

const handleCaseManageCloseTimeSelect = (times: any) => {
  highSearchForm.value.caseManageCloseStartTime = times[0] || ''
  highSearchForm.value.caseManageCloseEndTime = times[1] || ''
}

const handleEntrustsChangeForUpdate = () => {
  updateFormRef.value?.resetFields(['deputyTmplId', 'entrustsDeptId'])
  updateForm.value.fileList = []
  if (updateForm.value.entrustsId) {
    getDeputyTmplListForUpdate()
    getEntrustsDeptList(updateForm.value.entrustsId)
  }
}
const handleCaseUpdate = () => {
  updateVisible.value = true
}
const handleUpdateModalDownload = async () => {
  let params: any = {
    tmplId: updateForm.value.deputyTmplId,
    tmplType: '3' // 案件更新模板
  }
  let res = await downloadTmpl(params)
  Message.success('下载成功')
  sysUtil.blobExport(res)
}

const handleUpdateConfirm = async () => {
  const res = await updateFormRef.value?.validate()
  if (!res) {
    let list: any = []
    updateForm.value.fileList.map((item: any) => list.push(item.file))
    let param = new FormData()
    param.append('dateConfigType', updateForm.value.dateConfigType)
    param.append('deputyTmplId', updateForm.value.deputyTmplId)
    param.append('entrustsId', updateForm.value.entrustsId)
    param.append('entrustsDeptId', updateForm.value.entrustsDeptId)
    param.append('completeReview', String(updateForm.value.completeReview))
    param.append('isIgnoreError', String(updateForm.value.isIgnoreError))

    if (updateForm.value.dateConfigType === '2') {
      param.append('mediateBeginTime', updateForm.value.mediateBeginTime)
      param.append('mediateEndTime', updateForm.value.mediateEndTime)
    }
    param.append('impFlag', '2')
    if (list.length) {
      param.append('file', list[0])
    }
    const result: any = await caseBatchImport(param)
    if (result.success === false) {
      Modal.error({ title: '案件更新失败', content: result.massage, bodyStyle: 'white-space:pre-wrap' })
    } else {
      openImportReocrdDialog(result.massage)
    }
  }
  return false
}
const handleUpdateCancel = () => {
  updateForm.value = createImpFormModel()
  updateFormRef.value?.clearValidate()
  updateVisible.value = false
}
const handleUpdateFileChange: UploadInstance['onChange'] = (_, item) => {
  let type = item.name?.split('.')[1]
  if (type !== 'xls' && type !== 'xlsx') {
    Message.warning('请选择xls或者xlsx文件')
    updateForm.value.fileList = []
  }
}
// 案件导出
const exportTmplList = ref<Array<any>>([])
const exportVisible = ref<boolean>(false)
const exportColumns = computed<TableColumnData[]>(() => [
  { tooltip: true, ellipsis: true, title: '导出模板名称', dataIndex: 'tmplTitle' },
  { tooltip: true, ellipsis: true, title: '案源方', dataIndex: 'entrustsName' }
])
const selectedExportTemlKeys = ref<Array<any>>([])
const handleCaseExport = async () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  await getExportTmplList()
  exportVisible.value = true
}

const getExportTmplList = async () => {
  let param = { ifChooseAll: ifChooseAll.value, caseIds: selectedKeys.value }
  if (ifChooseAll.value) param['mediateCasePageBO'] = allSearchForm.value
  let res: any = await getExportTmpl(param)
  exportTmplList.value = res || []
}

const handleExportConfirm = async () => {
  if (!selectedExportTemlKeys.value.length) {
    Message.warning('请选择模板')
    return false
  }
  let deputyTmplIds = selectedExportTemlKeys.value
  let param = { ifChooseAll: ifChooseAll.value, deputyTmplIds, caseIds: selectedKeys.value }
  if (ifChooseAll.value) param['mediateCasePageBO'] = allSearchForm.value
  let res: any = await exportCase(param)
  if (res) {
    Message.success('案件导出成功')
    sysUtil.blobExport(res)
    handleExportCancel()
  }
}
const handleExportCancel = () => {
  selectedExportTemlKeys.value = []
  exportVisible.value = false
  exportTmplList.value = []
}
// 案件分派
const assignVisible = ref<boolean>(false)

const assignForm = ref<CASE_ASSIGN_FORM_TYPE>({
  orgType: needRule.value ? '3' : '2',
  currentMediatorId: '',
  deptId: '',
  orgId: ''
})

const handleCaseAssign = async () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  if (ifChooseAll.value && _.isEmpty(allSearchForm.value)) {
    Message.warning('正在进行全选分派操作，你未选择条件筛选！')
    return false
  }
  await getMediationOrganizationList()
  assignVisible.value = true
}
const handleMediationOrganizationChangeForAssign = () => {
  assignForm.value.deptId = ''
  assignForm.value.currentMediatorId = ''
  deptNameOptions.value = []
  currentMediatorNameOptions.value = []
  let arr = [assignForm.value.orgId]
  getMediationTeamList(arr)
}
const handleMediationTeamChangeForAssign = () => {
  assignForm.value.currentMediatorId = ''
  currentMediatorNameOptions.value = []
  let arr = [assignForm.value.deptId]
  getMediationStaffList(arr)
}
const handleAssignConfirm = async () => {
  const res = await assignFormRef.value?.validate()
  if (!res) {
    let params: any = {
      caseIdList: selectedKeys.value,
      ifChooseAll: ifChooseAll.value,
      targetMdtOrgId: assignForm.value.orgId
    }
    if (ifChooseAll.value) params['mediateCasePageBO'] = allSearchForm.value
    for (let i = 0; i < orgNameOptions.value.length; i++) {
      const element = orgNameOptions.value[i]
      if (element.value === assignForm.value.orgId) {
        params.targetMdtOrgName = element.label
        break
      }
    }
    if (assignForm.value.deptId) {
      params.targetDeptId = assignForm.value.deptId
      for (let i = 0; i < deptNameOptions.value.length; i++) {
        const element = deptNameOptions.value[i]
        if (element.value === assignForm.value.deptId) {
          params.targetDeptName = element.label
          break
        }
      }
    }
    if (assignForm.value.currentMediatorId) {
      params.targetMediatorId = assignForm.value.currentMediatorId
      for (let i = 0; i < currentMediatorNameOptions.value.length; i++) {
        const element = currentMediatorNameOptions.value[i]
        if (element.value === assignForm.value.currentMediatorId) {
          params.targetMediatorName = element.label
          break
        }
      }
    }
    let res: any = await findDistributeCase(params)
    if (res) {
      const distributeText = {
        0: `本次为${res.mediatorName}分派[${res.needDistributedAmount}]个案件，您是否确认进行此次分派？`,
        1: `本次为${res.mediatorName}分派[${res.needDistributedAmount}]个案件，其中[${res.distributedAmount}]个已被分派过，您是否确认进行此次分派？`
      }
      Modal.warning({
        title: '提示',
        hideCancel: false,
        alignCenter: true,
        content: distributeText[res.isIncludeDistributedCase],
        onOk: async () => {
          await caseDistribute(params)
          Message.success('案件分派成功')
          ifChooseAll.value = false
          handleAssignCancel()
          search()
        },
        onCancel: () => {}
      })
    }
  }
  return false
}
const handleAssignCancel = () => {
  assignForm.value = {
    orgType: needRule.value ? '3' : '2',
    currentMediatorId: '',
    orgId: '',
    deptId: ''
  }
  assignFormRef.value?.clearValidate()
  assignVisible.value = false
}

// 号码检测
const numberCheckModalRef = ref<InstanceType<typeof NumberCheckModal>>()
const numberCheckVisible = ref(false)
const handleBatchNumberCheck = () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  numberCheckVisible.value = true
}
// 失联修复
const callRepairModalRef = ref<InstanceType<typeof CallRepairModal>>()
const callRepairVisible = ref(false)
const callState = ref(0)
const handleBatchCallRepair = (state) => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  callRepairVisible.value = true
  callState.value = state
}
// 短信发送
const smsSendModalRef = ref<InstanceType<typeof SmsSendModal>>()
const smsSendVisible = ref(false)
const handleBatchSmsSend = () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  smsSendVisible.value = true
}

// 批量生成文书
const docGenerateModalRef = ref<InstanceType<typeof DocGenerateModal>>()
const docGenerateVisible = ref(false)
const handleBatchDocGenerate = () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  docGenerateVisible.value = true
}

// 详情导航
const handleToCaseDetails = (id: string) => {
  if (id) {
    router.push({ path: `caseDetails/${id}` })
  }
}

//案件任务分派
const taskAssiggVisible = ref(false)

const handleTaskAssigg = () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  taskAssiggVisible.value = true
}

//批量任务登记
const batchTaskRegisterVisible = ref(false)

const handleBatchTaskRegister = () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  if (ifChooseAll.value && _.isEmpty(allSearchForm.value)) {
    Message.warning('正在进行全选操作，你未选择条件筛选！')
    return false
  }
  batchTaskRegisterVisible.value = true
}

const handleBatchTaskRegisterSuccess = () => {
  batchTaskRegisterVisible.value = false
  ifChooseAll.value = false
  selectedKeys.value = []
  search()
}

const countNewLines = (str) => {
  const matches = str.match(/(\r\n|\n|\r)/g)
  return matches ? matches.length + 1 : str ? 1 : 0
}

//案件批量办结申请
const batchApprovalVisible = ref(false)
const businessType = ref('')
const handleBatchApproval = () => {
  if (!selectedKeys.value.length) {
    Message.warning('请选择案件')
    return false
  }
  batchApprovalVisible.value = true

  businessType.value = renderData.value.find((e) => selectedKeys.value.includes(e.caseId))?.businessType || '1'
}
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}

.form-title {
  display: flex;
  height: 20px;
  align-items: center;
  border-left: 4px solid $color-primary;
  font-size: 15px;
  font-weight: bold;
  padding-left: 10px;
  margin-bottom: 16px;
}
:deep(.arco-upload-progress) {
  display: none;
}

.height-32 {
  height: 30px;
}
</style>
