<template>
  <a-modal :visible="visible" title="Modal Form" @cancel="emit('closeForm','')" :footer="false" :width="600">
    <a-form :key="formKey" :model="editData" :rules="rules" @submit-success="submit">
      <a-form-item field="businessTypeName" label="业务类型名称">
        <a-input v-model="form.businessTypeName" />
      </a-form-item>
      <a-form-item label="父级" v-show="openType ==2 || openType==3">
        <BusinessTypeSelect v-model="form.parentId" :allowClear="true"></BusinessTypeSelect>
      </a-form-item>
      <a-form-item field="processTypeList" label="办结流程">
        <a-select v-model.number[]="form.processTypeList" placeholder="请选择办结流程" :multiple="true">
          <a-option :value="1">调解办结</a-option>
          <a-option :value="3">案管办结</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="status" label="流程状态">
        <a-switch v-model="switchValue" />
      </a-form-item>
      <a-form-item field="description" label="描述">
        <a-textarea v-model="form.description" placeholder="请填写描述" allow-clear/>
      </a-form-item>
      <a-form-item style="margin-top: 15px;">
        <a-space>
          <a-button @click="emit('closeForm','')">取消</a-button>
          <a-button type="primary" html-type="submit">确定</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts" name="EditForm">
import {ref,watch,computed} from 'vue';
import BusinessTypeSelect from '@/components/businessType/businessTypeSelect.vue'
import { submitBusinessType } from '@/api/arcoApi/systemManage/businessTypeManage.ts'
import { Message } from '@arco-design/web-vue'
const props = defineProps(['editData','visible','formKey','openType']);
const emit = defineEmits(['closeForm','reload'])
const rules = {
  businessTypeName: [
    {
      required: true,
      message: '业务类型名称必填'
    }
  ],
  status: [
    {
      required: true,
      message: '办结流程必选'
    }
  ],
  processTypeList: [
    {
      required: true,
      message: '办结流程必选'
    }
  ]
}

const form = ref<BUSINESS_TYPE>({
  businessTypeId: null,
  businessTypeName: '',
  description: '',
  status: 1,
  parentId: null,
  createTime: null,
  processTypeList: [],
  children: null
});
const switchValue = computed({
  get: () => form.value.status === 1, // 将1转换为true，2转换为false
  set: (val) => {
    form.value.status = val ? 1 : 0; // 将true转换为1，false转换为2
  }
});

// 提交
let submit = ()=>{
  submitBusinessType(form.value).then((res:any)=>{
    if(res){
      emit('closeForm','')
      Message.success(props.openType.value==1||props.openType.value==3?'添加成功':'修改成功');
      emit('reload','')
    }
  })
}

watch(
  () => props.editData,
  (newVal) => {
    if (newVal) {
      form.value = newVal;
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="css">

</style>
